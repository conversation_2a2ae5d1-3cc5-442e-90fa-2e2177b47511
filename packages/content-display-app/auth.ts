import NextAuth, { NextAuthResult } from "next-auth";
import { prisma } from "@repo/prisma/src/client";
import { PrismaAdapter } from "@auth/prisma-adapter";
import type { User } from "@prisma/client";
import authConfig from "./auth.config";

const nextAuthResult = NextAuth({
  adapter: PrismaAdapter(prisma),
  ...authConfig,
});

export const signIn: NextAuthResult["signIn"] = nextAuthResult.signIn;
export const { auth, signOut, handlers } = nextAuthResult;

declare module "next-auth" {
  interface Session {
    user: User;
  }
}

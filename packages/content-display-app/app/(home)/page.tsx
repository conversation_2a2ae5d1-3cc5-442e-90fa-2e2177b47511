import Image from "next/image";
import Link from "next/link";

import { formatArticlePath, formatDate } from "../utils";
import { cx } from "@repo/ui/src/utils/cx";
import {
  ArticleListResponse,
  queryArticleList,
} from "../api/read/article/list/query";
import { queryCategoriesByLanguage } from "../api/read/category/query";

const HomeEntry: React.FC<{ article: ArticleListResponse[0] }> = ({
  article,
}) => {
  if (!article) return null;
  const { id, title, imageUrl, Blog, language } = article;
  const { articleDate, slug } = Blog ?? {};
  const bannerUrl = imageUrl?.replace(
    "/blog-image/",
    "/blog-image-resize-400/",
  );
  return (
    <Link
      href={formatArticlePath({ id, slug, lang: language })}
      key={id}
      className={cx(
        "relative flex flex-col overflow-hidden w-full",
        "border rounded-md",
        "hover:shadow-lg transition-all cursor-pointer ",
      )}
    >
      <div className="relative aspect-[21/9] flex-shrink-0 rounded-sm overflow-hidden m-2 mb-0">
        {bannerUrl && (
          <Image fill src={bannerUrl} alt="cover" className="object-cover" />
        )}
      </div>
      <div className="p-5 space-y-2 flex-grow flex flex-col">
        <h2 className="text-xl font-bold flex-grow">{title}</h2>
        <p className="text-sm text-gray-400">{formatDate(articleDate)}</p>
      </div>
    </Link>
  );
};

const LATEST_LINK_PROPS = {
  id: undefined,
  labels: [
    {
      label: "Latest",
      language: "en",
    },
  ],
  _count: {
    blogs: 1,
  },
};

export default async function HomePage(props: {
  params: Promise<{ lang: string }>;
  searchParams: Promise<{ category?: string }>;
}) {
  const { params, searchParams } = props;
  const { lang } = await params;
  const { category } = await searchParams;
  const [blogs, categories] = await Promise.all([
    queryArticleList(lang, category),
    queryCategoriesByLanguage(lang),
  ]);

  return (
    <main className="w-full max-w-[1920px] min-h-full mx-auto p-5 md:p-10 space-y-10 flex flex-col">
      <div className="w-full text-xl flex gap-8 mr-auto">
        {[LATEST_LINK_PROPS, ...categories].map(
          ({ id, labels, _count: { blogs } }) => {
            if (!blogs) return null;
            return (
              <Link
                key={id ?? ""}
                href={id ? { query: { category: id } } : (lang ?? "/")}
                className={cx(
                  category === id && "font-bold underline underline-offset-4",
                )}
              >
                {
                  (
                    labels.find((l) => l.language === lang) ||
                    labels.find((l) => l.language === "en")
                  )?.label
                }
              </Link>
            );
          },
        )}
      </div>

      <div className="w-full grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 2xl:grid-cols-4 gap-5">
        {blogs?.map((blog) => {
          return <HomeEntry key={blog?.id} article={blog} />;
        })}
      </div>
    </main>
  );
}

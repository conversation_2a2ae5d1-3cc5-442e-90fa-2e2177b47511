import { auth } from "../../../../auth";
import { stripe } from "@repo/ui/src/utils/stripe";
import { NextResponse } from "next/server";

export async function POST(request: Request) {
  const { user } = (await auth()) ?? {};
  const userId = user?.id;
  const email = user?.email;
  const customerId = user?.stripeCustomerId ?? undefined;

  if (!userId) {
    return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
  }

  try {
    const session = await stripe.checkout.sessions.create({
      metadata: {
        user_id: userId,
      },
      ...(customerId ? { customer: customerId } : { customer_email: email }),
      payment_method_types: ["card"],
      line_items: [
        {
          price: "price_1QdSrsF2ZCO0uVYpr3jSQxJk",
          quantity: 1,
        },
      ],
      mode: "subscription",
      success_url: `${request.headers.get("origin")}/`,
      cancel_url: `${request.headers.get("origin")}/`,
    });

    return NextResponse.json({ id: session.id });
  } catch (error: unknown) {
    return NextResponse.json(
      {
        message:
          error instanceof Error ? error.message : "Internal Server Error",
      },
      { status: 500 },
    );
  }
}

{"extends": "typescript-config/nextjs.json", "compilerOptions": {"baseUrl": ".", "paths": {"@/*": ["./app/*"], "@repo/*": ["../../packages/*"], "ui/*": ["../../packages/ui/src/*"], "ui": ["../../packages/ui/index.tsx"], "prisma-db": ["../../packages/prisma/index.ts"], "prisma-db/*": ["../../packages/prisma/*"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules"]}
import {
  DeleteObjectCommand,
  ListObjectsV2Command,
  PutO<PERSON>Command,
  S3Client,
  S3ServiceException,
} from "@aws-sdk/client-s3";
import sharp from "sharp";

/**
 * Upload a file to an S3 bucket.
 */

const { S3_BUCKET_NAME, AWS_REGION, AWS_ACCESS_KEY_ID, AWS_SECRET_ACCESS_KEY } =
  process.env;

if (
  !S3_BUCKET_NAME ||
  !AWS_REGION ||
  !AWS_ACCESS_KEY_ID ||
  !AWS_SECRET_ACCESS_KEY
) {
  throw new Error("Missing environment variables");
}

export class ImageBucket {
  client = new S3Client({
    region: AWS_REGION!,
    credentials: {
      accessKeyId: AWS_ACCESS_KEY_ID!,
      secretAccessKey: AWS_SECRET_ACCESS_KEY!,
    },
  });

  listFiles = async () => {
    const command = new ListObjectsV2Command({
      Bucket: S3_BUCKET_NAME,
    });

    const response = await this.client.send(command);
    console.log(response);
    return response;
  };

  uploadFile = async ({
    key,
    file,
    resize = false,
  }: {
    key: string;
    file: File | Buffer;
    resize: boolean;
  }) => {
    const buffer =
      file instanceof Buffer ? file : ((await file?.arrayBuffer()) as Buffer);
    await this.upload({ key: `blog-image/${key}`, buffer });

    if (!resize) return;

    const buffer800 = await sharp(buffer)
      .webp({ quality: 80 })
      .resize(800, 800, { fit: "inside" })
      .toBuffer();
    await this.upload({
      key: `blog-image-resize-800/${key}`,
      buffer: buffer800,
    });

    const buffer400 = await sharp(buffer)
      .webp({ quality: 80 })
      .resize(400, 400, { fit: "inside" })
      .toBuffer();
    await this.upload({
      key: `blog-image-resize-400/${key}`,
      buffer: buffer400,
    });
  };

  upload = async ({ key, buffer }: { key: string; buffer: Buffer }) => {
    const command = new PutObjectCommand({
      Bucket: S3_BUCKET_NAME,
      Key: key,
      Body: buffer,
    });

    try {
      const response = await this.client.send(command);
      console.log(response);
    } catch (caught) {
      if (
        caught instanceof S3ServiceException &&
        caught.name === "EntityTooLarge"
      ) {
        console.error(
          `Error from S3 while uploading object to ${S3_BUCKET_NAME}. \
  The object was too large. To upload objects larger than 5GB, use the S3 console (160GB max) \
  or the multipart upload API (5TB max).`,
        );
      } else if (caught instanceof S3ServiceException) {
        console.error(
          `Error from S3 while uploading object to ${S3_BUCKET_NAME}.  ${caught.name}: ${caught.message}`,
        );
      } else {
        throw caught;
      }
    }
  };

  deleteFile = async ({ key }: { key: string }) => {
    const command = new DeleteObjectCommand({
      Bucket: S3_BUCKET_NAME,
      Key: key,
    });

    try {
      const response = await this.client.send(command);
      console.log(response);
    } catch (caught) {
      if (caught instanceof S3ServiceException) {
        console.error(
          `Error from S3 while deleting object to ${S3_BUCKET_NAME}.  ${caught.name}: ${caught.message}`,
        );
      } else {
        throw caught;
      }
    }
  };
}

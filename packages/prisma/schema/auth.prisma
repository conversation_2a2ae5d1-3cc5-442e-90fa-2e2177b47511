model User {
  id                   String          @id @default(cuid())
  name                 String?
  email                String          @unique
  emailVerified        DateTime?
  image                String?
  role                 Role            @default(USER)
  createdAt            DateTime        @default(now())
  updatedAt            DateTime        @updatedAt
  subscriptionStatus   Boolean         @default(false)
  stripeSubscriptionId String?
  stripeCustomerId     String?
  accounts             Account[]
  Authenticator        Authenticator[]
  sessions             Session[]
  spaces               Space[]
}

model Account {
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  user              User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@id([provider, providerAccountId])
}

model Session {
  sessionToken String   @unique
  userId       String
  expires      DateTime
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model VerificationToken {
  identifier String
  token      String
  expires    DateTime

  @@id([identifier, token])
}

model Authenticator {
  credentialID         String  @unique
  userId               String
  providerAccountId    String
  credentialPublicKey  String
  counter              Int
  credentialDeviceType String
  credentialBackedUp   Boolean
  transports           String?
  user                 User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@id([userId, credentialID])
}

enum Role {
  ADMIN
  AUTHOR
  USER
}

model Space {
  id          String     @id @default(cuid())
  title       String
  description String
  ownerId     String
  blogs       Blog[]
  owner       User       @relation(fields: [ownerId], references: [id], onDelete: Cascade)
  Category    Category[]
}

model Blog {
  id          String        @id @default(cuid())
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt
  isPremium   Boolean
  isPublished Boolean
  articleDate DateTime
  slug        String
  spaceId     String
  Space       Space         @relation(fields: [spaceId], references: [id], onDelete: Cascade)
  contents    BlogContent[]
  tags        String[]
  categories  Category[]
}

model BlogContent {
  id             String   @id @default(cuid())
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt
  isReady        Boolean  @default(false)
  title          String
  imageUrl       String
  seoDescription String
  content        String
  language       String
  blogId         String?
  Blog           Blog?    @relation(fields: [blogId], references: [id], onDelete: Cascade)
}

model Category {
  id      String          @id @default(cuid())
  spaceId String
  Space   Space           @relation(fields: [spaceId], references: [id], onDelete: Cascade)
  blogs   Blog[]
  labels  CategoryLabel[]
}

model CategoryLabel {
  id         String    @id @default(cuid())
  Category   Category? @relation(fields: [categoryId], references: [id], onDelete: Cascade)
  categoryId String?
  label      String
  language   String
}

import NextAuth, { NextAuthResult } from "next-auth";
import { prisma } from "prisma-db";
import { PrismaAdapter } from "@auth/prisma-adapter";
import authConfig from "./auth.config";
import type { User } from "@prisma/client";

declare module "next-auth" {
  interface Session {
    user: User;
  }
}

const nextAuthResult = NextAuth({
  adapter: PrismaAdapter(prisma),
  ...authConfig,
});

export const signIn: NextAuthResult["signIn"] = nextAuthResult.signIn;
export const auth: NextAuthResult["auth"] = nextAuthResult.auth;

export const { handlers, signOut } = nextAuthResult;

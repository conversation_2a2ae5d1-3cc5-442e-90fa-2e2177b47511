{"name": "content-config-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --port 3001", "build": "next build", "start": "next start --port 3001", "lint": "next lint", "type-check": "tsc --noEmit", "clean": "rm -rf .next dist"}, "dependencies": {"@ant-design/icons": "^5.5.1", "@ant-design/v5-patch-for-react-19": "^1.0.3", "@auth/prisma-adapter": "^2.5.3", "@aws-sdk/client-s3": "^3.664.0", "@langchain/core": "^0.3.10", "@langchain/google-genai": "^0.2.16", "@langchain/openai": "^0.6.3", "@mdxeditor/editor": "^3.20.0", "@next/third-parties": "^15.1.5", "@prisma/client": "^5.20.0", "@radix-ui/react-icons": "^1.3.0", "@stripe/stripe-js": "^4.10.0", "@tanstack/react-query": "^5.59.0", "antd": "^5.23.1", "dotenv": "^17.2.1", "highlight.js": "^11.10.0", "next": "^15.4.5", "next-auth": "^5.0.0-beta.29", "openai": "^4.67.3", "react": "^19", "react-dom": "^19", "react-icons": "^5.3.0", "react-markdown": "^9.0.1", "react-router-dom": "^6.26.2", "rehype-raw": "^7.0.0", "slugify": "^1.6.6", "stripe": "^17.1.0", "zod": "^3.25.76"}, "devDependencies": {"@tailwindcss/typography": "^0.5.15", "@types/node": "^20", "@types/react": "^19", "eslint": "^8", "eslint-config-next": "^15.4.5", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "fast-csv": "^5.0.1", "prettier": "3.3.3", "prisma": "^5.20.0", "prisma-db": "workspace:*", "tailwindcss": "^3.4.1", "typescript": "^5", "typescript-config": "workspace:*", "ui": "workspace:*"}}
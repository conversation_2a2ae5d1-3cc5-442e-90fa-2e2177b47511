"use client";
import { useDarkMode } from "@repo/ui/src/utils/use-dark-mode";
import { ConfigProvider, theme } from "antd";
import { PropsWithChildren } from "react";
import "@ant-design/v5-patch-for-react-19";

export const AntDesignConfigProvider = ({ children }: PropsWithChildren) => {
  const isDarkMode = useDarkMode();

  return (
    <ConfigProvider
      theme={{
        token: {
          colorPrimary: "#00b96b",
          colorBgLayout: "transparaent",
          colorLink: "#00b96b",
          borderRadius: 12,
          controlHeight: 40,
          controlHeightLG: 44,
          fontSize: 14,
          fontSizeLG: 16,
          fontSizeHeading2: 24,
          fontFamily: `"Noto Sans TC", sans-serif`,
        },
        algorithm: isDarkMode
          ? [theme.darkAlgorithm]
          : [theme.defaultAlgorithm],
      }}
    >
      {children}
    </ConfigProvider>
  );
};

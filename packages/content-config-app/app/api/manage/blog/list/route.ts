import { auth } from "@/auth";
import { prisma } from "@repo/prisma";

const spaceId = process.env.SPACE_ID ?? "";

const query = () =>
  prisma.blog.findMany({
    where: { spaceId },
    include: { contents: { orderBy: { language: "asc" } } },
    orderBy: { articleDate: "desc" },
  });

export type BlogListResponse = Awaited<ReturnType<typeof query>>;

export async function GET() {
  const res = await auth();
  if (!res?.user || res.user.role !== "ADMIN") {
    return Response.json({ message: "Unauthorized" }, { status: 401 });
  }

  try {
    const blogs = await query();
    return Response.json(blogs);
  } catch (error) {
    return Response.json(
      {
        message: "query blog failed",
        error,
      },
      { status: 400 },
    );
  }
}

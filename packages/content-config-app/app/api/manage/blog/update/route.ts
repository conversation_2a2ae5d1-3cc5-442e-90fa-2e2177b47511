import { auth } from "@/auth";
import { prisma } from "@repo/prisma";
import { Prisma } from "@prisma/client";

export type UpdateBlogInput = {
  blog: Partial<
    Omit<Prisma.BlogUncheckedCreateInput, "space" | "categories"> & {
      categories: Prisma.BlogUncheckedUpdateInput["categories"];
    }
  >;
  content: Partial<Omit<Prisma.BlogContentUncheckedCreateInput, "space">>;
};

export async function POST(req: Request) {
  const res = await auth();
  if (!res?.user || res.user.role !== "ADMIN") {
    return Response.json({ message: "Unauthorized" }, { status: 401 });
  }

  try {
    const data: UpdateBlogInput = await req.json();

    if (!data.blog.id) {
      return Response.json({ message: "Bad input" }, { status: 400 });
    }

    const blog = await prisma.blog.update({
      where: { id: data.blog.id },
      data: {
        ...data.blog,
        contents: {
          update: {
            where: { id: data.content.id },
            data: { ...data.content },
          },
        },
      },
    });

    return Response.json({ blog });
  } catch (error) {
    return Response.json(
      {
        message: "update blog failed",
        error,
      },
      { status: 400 },
    );
  }
}

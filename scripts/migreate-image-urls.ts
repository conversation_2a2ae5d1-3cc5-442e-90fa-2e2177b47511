import { prisma } from "@repo/prisma"
import dotenv from "dotenv";
dotenv.config();

async function main() {
  const list = await prisma.blogContent.findMany();

  for (const entry of list) {
    const newImageUrl = entry.imageUrl.replace(
      "https://shoshin-space.s3.ap-southeast-1.amazonaws.com",
      "https://shoshin-space.s3.ap-southeast-1.amazonaws.com/blog-image",
    );

    await prisma.blogContent.update({
      where: { id: entry.id },
      data: { imageUrl: newImageUrl },
    });
  }
}
main();

import dotenv from "dotenv";
import sharp from "sharp";
dotenv.config();

async function main() {
  const bucket = new (await import("@/libs/s3")).ImageBucket();
  const list = await bucket.listFiles();

  for (const entry of list.Contents ?? []) {
    const { Key } = entry;
    const url = `https://shoshin-space.s3.ap-southeast-1.amazonaws.com/${Key}`;

    const res = await fetch(url);
    const buffer = await res.arrayBuffer();

    const buffer800 = await sharp(buffer)
      .webp({ quality: 80 })
      .resize(800, 800, { fit: "inside" })
      .toBuffer();

    const buffer400 = await sharp(buffer)
      .webp({ quality: 80 })
      .resize(400, 400, { fit: "inside" })
      .toBuffer();

    await bucket.upload({ key: `resize800/${Key}`, buffer: buffer800 });
    await bucket.upload({ key: `resize400/${Key}`, buffer: buffer400 });
  }
}
main();
